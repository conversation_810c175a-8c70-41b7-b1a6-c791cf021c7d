package com.cy.kiwi.uc.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("修改自己密码")
public class PasswordUpdateVO {
    @ApiModelProperty("旧密码")
    @NotBlank
    private String oldPwd;

    @ApiModelProperty("新密码")
    @NotBlank
    private String pwd;
    @ApiModelProperty("确认密码")
    @NotBlank
    private String confirmPwd;
}
