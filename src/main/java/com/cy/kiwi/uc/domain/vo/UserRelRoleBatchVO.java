package com.cy.kiwi.uc.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("员工批量角色")
public class UserRelRoleBatchVO {
    @ApiModelProperty("员工id")
    @NotNull
    @NotEmpty
    private Set<Long> userIds;
    @ApiModelProperty("角色id")
    @NotNull
    private Long roleId;
}
