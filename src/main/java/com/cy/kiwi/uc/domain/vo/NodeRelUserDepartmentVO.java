package com.cy.kiwi.uc.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 使用节点的部门和员工对象 node_rel_user_department
 *
 * <AUTHOR>
 * @date 2020-12-29 15:52:03
 */
@Data
@ApiModel(value = "NodeRelUserDepartment对象", description = "使用节点的部门和员工（node_rel_user_department）")
public class NodeRelUserDepartmentVO {

    private Long id;

    @ApiModelProperty(value = "node.id")
    private Long nodeId;

    @ApiModelProperty(value = "关联的 id，user.id 或 department.id")
    private Long relId;

    @ApiModelProperty(value = "关联的类型，1 = user或2 = department")
    private Integer relType;

    @ApiModelProperty(value = "排序")
    private Long sort;
    @ApiModelProperty("员工名字或部门名字")
    private String name;

}
