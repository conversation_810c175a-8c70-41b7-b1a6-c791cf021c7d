package com.cy.kiwi.uc.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * 员工表对象 user
 *
 * <AUTHOR>
 * @date 2020-12-07 13:59:42
 */
@Data
@ApiModel(value = "浙商User对象", description = "浙商员工表（user）")
public class UserZsDetailVO {

    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty(value = "账号")
    private String userid;
    @ApiModelProperty(value = "成员名称")
    private String name;
    @ApiModelProperty("性别。0表示未定义，1表示男性，2表示女性")
    private String gender;

    @ApiModelProperty("企业邮箱")
    private String bizEmail;
    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "座机")
    private String telephone;
    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "地址")
    private String address;


    @ApiModelProperty("部门")
    private Set<String> depts;
    @ApiModelProperty(value = "职务信息")
    private String position;
    @ApiModelProperty("部门负责人")
    private Boolean leader;
    @ApiModelProperty("直属上级")
    private Set<String> directLeader;
}
