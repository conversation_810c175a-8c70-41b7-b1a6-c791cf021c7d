package com.cy.kiwi.uc.constant;

/**
 * 
 * 权限常量
 * 
 * <AUTHOR>
 * @version [v0.0.1, 2016年8月2日]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public interface AuthConsts {

    /**
     * 拥有当前路径权限：*
     */
    String ALL_PERMISSION = "*";

    /**
     * 拥有所子路径权限：**
     */
    String ALL_CHILD_PER = "**";

    /**
     * 分隔符：,
     */
    String PER_DELIMITER = ",";

    /**
     * url路径符：/
     */
    String URL_DELIMITER = "/";

    /**
     * 
     * 权限类型
     * 
     * <AUTHOR>
     * @version [v0.0.1, 2016年8月2日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    interface Type{

        /**
         * 权限类型：菜单
         */
        int MENU = 1;

        /**
         * 权限类型：模块
         */
        int MODEL = 2;

    }

    interface Role{

        int TYPE_BASE = 1;

        int TYPE_NORMAL = 2;
    }
}
