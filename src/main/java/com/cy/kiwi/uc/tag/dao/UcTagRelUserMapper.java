package com.cy.kiwi.uc.tag.dao;

import com.cy.kiwi.uc.tag.domain.bo.UcTagUserBO;
import com.cy.kiwi.uc.tag.domain.entity.UcTagRelUser;
import com.cy.kiwi.uc.tag.domain.vo.UcTagSelectResVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface UcTagRelUserMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UcTagRelUser entity);

    UcTagRelUser selectByPrimaryKey(Long id);

    int updateByPrimaryKey(UcTagRelUser entity);

    int deleteByTagId(@Param("tagId") Long tagId);

    int insertList(@Param("list") List<UcTagRelUser> list);

    int deleteByTagIdAndRelTypeAndRelIdIn(@Param("tagId") Long tagId, @Param("relType") Integer relType, @Param("relIdCollection") Collection<Long> relIdCollection);

    List<UcTagUserBO> selectUcTagUserByRelTypeAndRelIdIn(@Param("type") Integer type, @Param("list") Collection<Long> relIds);

    /**
     * 根据标签查询关联员工
     *
     * @param tagIds 标签id集合
     * @return 结果
     */
    List<Long> selectByTags(@Param("tagIds") List<Long> tagIds);

    List<UcTagRelUser> selectByTagIdAndRelTypeAndRelId(@Param("tagId")Long tagId,@Param("relType")Integer relType,@Param("relId")Long relId);

    List<UcTagRelUser> selectByTagIdAndRelTypeAndRelIdNotIn(@Param("tagId")Long tagId,@Param("relType")Integer relType,@Param("relIdCollection")Collection<Long> relIdCollection);

    List<Long> selectTagIdByRelTypeAndRelId(@Param("relType")Integer relType,@Param("relId")Long relId);





}