package com.cy.kiwi.uc.service.impl;

import com.cy.kiwi.common.constant.DBConsts;
import com.cy.kiwi.uc.domain.entity.MenuRelModel;
import com.cy.kiwi.uc.dao.MenuRelModelMapper;
import com.cy.kiwi.uc.service.IMenuRelModelService;

import javax.annotation.Resource;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * the service implement of MenuRelModel
 *
 * <AUTHOR>
 * @date 2020-12-05 16:44:11.0915
 */
@Service("menuRelModelService")
public class MenuRelModelServiceImpl implements IMenuRelModelService {

    private final static Logger log = LoggerFactory.getLogger(MenuRelModelServiceImpl.class);

    @Resource
    MenuRelModelMapper menuRelModelMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        log.info("Delete by id = {}", id);
        return menuRelModelMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(MenuRelModel record) {
        return null == record ? DBConsts.FAIL : menuRelModelMapper.insert(record);
    }

    @Override
    public int insertSelective(MenuRelModel record) {
        return null == record ? DBConsts.FAIL : menuRelModelMapper.insertSelective(record);
    }

    @Override
    public MenuRelModel selectByPrimaryKey(Long id) {
        log.info("Get by id = {}", id);
        return null == id ?
                null : menuRelModelMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(MenuRelModel record) {
        log.info("Update selective by id. {}", record);
        return null == record || null == record.getId() ?
                DBConsts.FAIL : menuRelModelMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(MenuRelModel record) {
        log.info("Update by id. {}", record);
        return null == record || null == record.getId() ?
                DBConsts.FAIL : menuRelModelMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<MenuRelModel> selectSelective(MenuRelModel record) {
        log.info("select by {}", record);
        record = null == record ? new MenuRelModel() : record;
        return menuRelModelMapper.selectSelective(record);
    }

    @Override
    public List<MenuRelModel> selectByPrimarys(List<Long> ids) {
        return null == ids ? null : menuRelModelMapper.selectByPrimarys(ids);
    }

    @Override
    public int deleteByMenuId(Long roleId) {
        return menuRelModelMapper.deleteByMenuId(roleId);
    }

    @Override
    public int insertBatch(List<MenuRelModel> records) {
        return menuRelModelMapper.insertBatch(records);
    }


}